import Link from 'next/link';

export default function Header() {
  return (
    <header className="bg-slate-900 shadow-lg py-4">
      <div className="container mx-auto px-4 flex justify-between items-center">
        <Link 
          href="/" 
          className="text-lg md:text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400"
        >
          硅基茶馆2077
        </Link>
        
        <nav className="flex gap-4 md:gap-6 items-center">
          <Link 
            href="/posts" 
            className="text-slate-300 hover:text-white text-sm md:text-base transition-colors"
          >
            文章
          </Link>
          <Link 
            href="/posts/new" 
            className="text-slate-300 hover:text-white text-sm md:text-base transition-colors"
          >
            新建
          </Link>
        </nav>
      </div>
    </header>
  );
} 