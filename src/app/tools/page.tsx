import Link from 'next/link';
import { Header } from '@/components/layout';

export default function ToolsPage() {
  const tools = [
    {
      id: 'article-converter',
      title: '文章页面转换器',
      description: '将基础 HTML 代码转换为使用项目组件库的高质量文章页面，适配微信公众号样式规范。',
      icon: '📄',
      href: '/tools/article-converter',
      features: [
        '智能组件映射 (img → ImageContainer)',
        '自动章节结构化 (Section + Divider)',
        '代码块和提示框转换',
        '表格样式优化',
        '完整页面代码生成'
      ]
    },
    {
      id: 'html-to-tsx',
      title: 'HTML 到 TSX 转换器',
      description: '将原生 HTML 代码转换为 React TSX 语法，支持属性名转换、自闭合标签、内联样式对象化等功能。',
      icon: '🔄',
      href: '/tools/html-to-tsx',
      features: [
        '属性名自动转换 (class → className)',
        '自闭合标签格式化',
        '内联样式对象化',
        '事件处理器转换',
        '实时预览和验证'
      ]
    },
    {
      id: 'cover-generator',
      title: '封面生成器',
      description: '为微信公众号文章生成专业的封面图片，支持多种预设模板和自定义配置。',
      icon: '🎨',
      href: '/cover',
      features: [
        '多种预设模板',
        '自定义标题和副标题',
        '高质量图片生成',
        '一键复制到剪贴板',
        '移动端适配'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 mb-4">
            开发工具集
          </h1>
          <p className="text-xl text-slate-300 mb-8">
            提升开发效率的实用工具集合
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 max-w-6xl mx-auto">
          {tools.map((tool) => (
            <Link key={tool.id} href={tool.href}>
              <div className="group bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-8 hover:bg-slate-800/70 hover:border-slate-600/50 transition-all duration-200 cursor-pointer h-full">
                <div className="flex items-start gap-4 mb-6">
                  <div className="text-4xl">{tool.icon}</div>
                  <div className="flex-1">
                    <h2 className="text-2xl font-bold text-white group-hover:text-cyan-400 transition-colors mb-2">
                      {tool.title}
                    </h2>
                    <p className="text-slate-300 leading-relaxed">
                      {tool.description}
                    </p>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-cyan-400 mb-3">主要功能</h3>
                  <ul className="space-y-2">
                    {tool.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2 text-slate-300">
                        <span className="w-1.5 h-1.5 bg-cyan-400 rounded-full"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="flex items-center justify-between pt-4 border-t border-slate-700/50">
                  <div className="flex items-center gap-2 text-xs text-slate-500">
                    <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span>可用</span>
                  </div>
                  <div className="text-cyan-400 group-hover:text-cyan-300 transition-colors font-medium">
                    使用工具 →
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-slate-800/50 rounded-full text-slate-400">
            <span className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></span>
            更多工具正在开发中...
          </div>
        </div>
      </main>
    </div>
  );
}
