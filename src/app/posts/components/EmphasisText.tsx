import React from 'react';

interface EmphasisTextProps {
  children: React.ReactNode;
  variant?: 'default' | 'highlight' | 'accent';
  size?: 'sm' | 'md' | 'lg';
}

export function EmphasisText({ 
  children, 
  variant = 'default',
  size = 'md'
}: EmphasisTextProps) {
  const getStyles = () => {
    const baseStyles = {
      fontWeight: 'bold',
      display: 'inline-block',
      padding: '8px 16px',
      borderRadius: '8px',
      margin: '12px 0',
      position: 'relative' as const,
      transition: 'all 0.3s ease',
    };

    const sizeStyles = {
      sm: { fontSize: '14px', padding: '6px 12px' },
      md: { fontSize: '16px', padding: '8px 16px' },
      lg: { fontSize: '18px', padding: '10px 20px' }
    };

    switch (variant) {
      case 'highlight':
        return {
          ...baseStyles,
          ...sizeStyles[size],
          background: 'linear-gradient(135deg, rgba(255, 107, 179, 0.15) 0%, rgba(0, 212, 255, 0.15) 100%)',
          border: '1px solid rgba(255, 107, 179, 0.3)',
          color: '#ff6bb3',
          textShadow: '0 0 10px rgba(255, 107, 179, 0.4)',
          boxShadow: '0 0 15px rgba(255, 107, 179, 0.2)',
        };
      case 'accent':
        return {
          ...baseStyles,
          ...sizeStyles[size],
          background: 'linear-gradient(135deg, rgba(0, 212, 255, 0.15) 0%, rgba(57, 255, 20, 0.15) 100%)',
          border: '1px solid rgba(0, 212, 255, 0.3)',
          color: '#00d4ff',
          textShadow: '0 0 10px rgba(0, 212, 255, 0.4)',
          boxShadow: '0 0 15px rgba(0, 212, 255, 0.2)',
        };
      default:
        return {
          ...baseStyles,
          ...sizeStyles[size],
          background: 'linear-gradient(135deg, rgba(255, 107, 179, 0.08) 0%, rgba(255, 107, 179, 0.02) 100%)',
          borderLeft: '4px solid #ff6bb3',
          color: '#ff6bb3',
          textShadow: '0 0 8px rgba(255, 107, 179, 0.3)',
          paddingLeft: '16px',
        };
    }
  };

  return (
    <div className="emphasis-text" style={getStyles()}>
      {children}
    </div>
  );
}
