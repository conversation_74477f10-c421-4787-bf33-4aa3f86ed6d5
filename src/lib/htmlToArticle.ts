/**
 * HTML 到高质量文章页面转换工具
 * 将基础 HTML/TSX 转换为使用项目组件库的高质量文章页面代码
 */

import { convertHtmlToTsx } from './htmlToTsx';

// 组件映射规则
interface ComponentMapping {
  selector: string;
  component: string;
  props?: Record<string, any>;
  wrapper?: boolean;
}

// 预定义的组件映射规则
const COMPONENT_MAPPINGS: ComponentMapping[] = [
  // 图片容器
  {
    selector: 'img',
    component: 'ImageContainer',
    props: { width: '100%', height: 'auto' }
  },
  // 代码块
  {
    selector: 'pre code, code[class*="language-"]',
    component: 'CodeBlock',
    props: { language: 'javascript' }
  },
  // 提示框 - 基于类名或特定结构
  {
    selector: '.alert, .notice, .warning, .info, .success, .error',
    component: 'HighlightBox',
    props: { type: 'info' }
  },
  // 引用块转换为提示框
  {
    selector: 'blockquote',
    component: 'HighlightBox',
    props: { type: 'info' }
  },
  // 表格保持原样但添加样式
  {
    selector: 'table',
    component: 'table',
    props: { style: { width: '100%', borderCollapse: 'collapse', marginBottom: '1rem' } }
  }
];

/**
 * 分析 HTML 结构并生成组件转换建议
 */
export function analyzeHtmlStructure(html: string): {
  suggestions: string[];
  componentUsage: Record<string, number>;
  sections: number;
} {
  const suggestions: string[] = [];
  const componentUsage: Record<string, number> = {};
  
  // 分析图片
  const imgMatches = html.match(/<img[^>]*>/g) || [];
  if (imgMatches.length > 0) {
    componentUsage['ImageContainer'] = imgMatches.length;
    suggestions.push(`发现 ${imgMatches.length} 个图片，建议使用 ImageContainer 组件`);
  }
  
  // 分析代码块
  const codeMatches = html.match(/<pre[^>]*>[\s\S]*?<\/pre>|<code[^>]*>[\s\S]*?<\/code>/g) || [];
  if (codeMatches.length > 0) {
    componentUsage['CodeBlock'] = codeMatches.length;
    suggestions.push(`发现 ${codeMatches.length} 个代码块，建议使用 CodeBlock 组件`);
  }
  
  // 分析引用块
  const blockquoteMatches = html.match(/<blockquote[^>]*>[\s\S]*?<\/blockquote>/g) || [];
  if (blockquoteMatches.length > 0) {
    componentUsage['HighlightBox'] = (componentUsage['HighlightBox'] || 0) + blockquoteMatches.length;
    suggestions.push(`发现 ${blockquoteMatches.length} 个引用块，建议转换为 HighlightBox 组件`);
  }
  
  // 分析标题结构
  const h2Matches = html.match(/<h2[^>]*>[\s\S]*?<\/h2>/g) || [];
  const sections = h2Matches.length;
  if (sections > 1) {
    componentUsage['Section'] = sections;
    componentUsage['Divider'] = sections - 1;
    suggestions.push(`发现 ${sections} 个主要章节，建议使用 Section 和 Divider 组件`);
  }
  
  // 分析表格
  const tableMatches = html.match(/<table[^>]*>[\s\S]*?<\/table>/g) || [];
  if (tableMatches.length > 0) {
    suggestions.push(`发现 ${tableMatches.length} 个表格，建议添加响应式样式`);
  }
  
  return { suggestions, componentUsage, sections };
}

/**
 * 转换图片为 ImageContainer 组件
 */
function convertImages(content: string): string {
  return content.replace(
    /<img([^>]*?)src="([^"]*)"([^>]*?)alt="([^"]*)"([^>]*?)>/g,
    (match, before, src, middle, alt, after) => {
      // 提取其他属性
      const widthMatch = match.match(/width="([^"]*)"/);
      const heightMatch = match.match(/height="([^"]*)"/);
      
      let props = `src="${src}" alt="${alt}"`;
      if (widthMatch) props += ` width="${widthMatch[1]}"`;
      if (heightMatch) props += ` height="${heightMatch[1]}"`;
      
      return `<ImageContainer ${props} />`;
    }
  );
}

/**
 * 转换代码块为 CodeBlock 组件
 */
function convertCodeBlocks(content: string): string {
  // 转换带语言标识的代码块
  content = content.replace(
    /<pre><code class="language-(\w+)">([\s\S]*?)<\/code><\/pre>/g,
    (match, language, code) => {
      const cleanCode = code.trim().replace(/`/g, '\\`');
      return `<CodeBlock language="${language}">\n{\`${cleanCode}\`}\n</CodeBlock>`;
    }
  );

  // 转换普通代码块
  content = content.replace(
    /<pre><code>([\s\S]*?)<\/code><\/pre>/g,
    (match, code) => {
      const cleanCode = code.trim().replace(/`/g, '\\`');
      return `<CodeBlock language="text">\n{\`${cleanCode}\`}\n</CodeBlock>`;
    }
  );

  // 转换单独的 pre 标签
  content = content.replace(
    /<pre[^>]*>([\s\S]*?)<\/pre>/g,
    (match, code) => {
      const cleanCode = code.trim().replace(/`/g, '\\`');
      return `<CodeBlock language="text">\n{\`${cleanCode}\`}\n</CodeBlock>`;
    }
  );

  return content;
}

/**
 * 转换引用块为 HighlightBox 组件
 */
function convertBlockquotes(content: string): string {
  return content.replace(
    /<blockquote[^>]*>([\s\S]*?)<\/blockquote>/g,
    (match, innerContent) => {
      const cleanContent = innerContent.trim();
      return `<HighlightBox type="info">\n${cleanContent}\n</HighlightBox>`;
    }
  );
}

/**
 * 添加 Section 和 Divider 组件
 */
function addSectionsAndDividers(content: string): string {
  // 检查是否有 h2 标签
  const h2Matches = content.match(/<h2[^>]*>/g);
  if (!h2Matches || h2Matches.length === 0) {
    return content;
  }

  // 在 h2 标签前添加 Divider（除了第一个）
  let isFirstH2 = true;
  content = content.replace(/<h2([^>]*)>/g, (match) => {
    if (isFirstH2) {
      isFirstH2 = false;
      return `<Section>\n${match}`;
    }
    return `</Section>\n\n<Divider />\n\n<Section>\n${match}`;
  });

  // 在最后添加 Section 结束标签
  if (content.includes('<Section>')) {
    content += '\n</Section>';
  }

  return content;
}

/**
 * 优化表格样式
 */
function optimizeTables(content: string): string {
  return content.replace(
    /<table([^>]*)>/g,
    '<div style={{ overflowX: \'auto\' }}>\n<table style={{ width: \'100%\', borderCollapse: \'collapse\', marginBottom: \'1rem\' }}>'
  ).replace(
    /<\/table>/g,
    '</table>\n</div>'
  ).replace(
    /<th([^>]*)>/g,
    '<th style={{ padding: \'12px\', border: \'1px solid #ddd\', textAlign: \'left\', backgroundColor: \'#f5f5f5\' }}>'
  ).replace(
    /<td([^>]*)>/g,
    '<td style={{ padding: \'12px\', border: \'1px solid #ddd\' }}>'
  );
}

/**
 * 生成完整的文章页面代码
 */
export function generateArticlePage(
  html: string,
  metadata: {
    title: string;
    date: string;
    excerpt: string;
    componentName?: string;
  }
): string {
  // 1. 先进行基础 HTML 到 TSX 转换
  let content = convertHtmlToTsx(html);
  
  // 2. 转换为组件库组件
  content = convertImages(content);
  content = convertCodeBlocks(content);
  content = convertBlockquotes(content);
  content = optimizeTables(content);
  content = addSectionsAndDividers(content);
  
  // 3. 生成完整的页面代码
  const componentName = metadata.componentName || 'ArticlePage';

  // 检查使用了哪些组件
  const usedComponents = [];
  if (content.includes('<ImageContainer')) usedComponents.push('ImageContainer');
  if (content.includes('<HighlightBox')) usedComponents.push('HighlightBox');
  if (content.includes('<CodeBlock')) usedComponents.push('CodeBlock');
  if (content.includes('<Section')) usedComponents.push('Section');
  if (content.includes('<Divider')) usedComponents.push('Divider');

  const importStatement = usedComponents.length > 0
    ? `import { ${usedComponents.join(', ')} } from '../components';`
    : '// No components needed';

  // 缩进内容
  const indentedContent = content
    .split('\n')
    .map(line => line.trim() ? `        ${line}` : line)
    .join('\n');

  const pageCode = `'use client';

import React from 'react';
import ArticleLayout, { ArticleMetadata } from '@/components/layout/ArticleLayout';
${importStatement}

export default function ${componentName}() {
  const metadata: ArticleMetadata = {
    title: '${metadata.title.replace(/'/g, "\\'")}',
    date: '${metadata.date}',
    excerpt: '${metadata.excerpt.replace(/'/g, "\\'")}',
  };

  return (
    <ArticleLayout metadata={metadata}>
      <div className="article-content">
${indentedContent}
      </div>
    </ArticleLayout>
  );
}`;

  return pageCode;
}

/**
 * 提取文章元数据
 */
export function extractMetadata(html: string): {
  title?: string;
  excerpt?: string;
  suggestedDate: string;
} {
  // 提取标题
  const titleMatch = html.match(/<h1[^>]*>(.*?)<\/h1>/);
  const title = titleMatch ? titleMatch[1].replace(/<[^>]*>/g, '').trim() : undefined;
  
  // 提取摘要（第一个段落）
  const pMatch = html.match(/<p[^>]*>(.*?)<\/p>/);
  const excerpt = pMatch ? pMatch[1].replace(/<[^>]*>/g, '').trim().substring(0, 100) + '...' : undefined;
  
  // 生成建议的日期
  const today = new Date();
  const suggestedDate = today.toISOString().split('T')[0];
  
  return { title, excerpt, suggestedDate };
}

/**
 * 生成组件使用指南
 */
export function generateComponentGuide(): string {
  return `
## 📚 组件库使用指南

### 可用组件

1. **ImageContainer** - 图片容器
   \`\`\`tsx
   <ImageContainer 
     src="/posts/YYYY-MM-DD/img/image.png" 
     alt="图片描述" 
     caption="图片说明（可选）"
     width="100%"
     height="auto"
   />
   \`\`\`

2. **HighlightBox** - 提示框
   \`\`\`tsx
   <HighlightBox type="info" title="标题（可选）">
     <p>内容可以包含任何 JSX 元素</p>
   </HighlightBox>
   \`\`\`
   支持的类型：\`info\`, \`warning\`, \`success\`, \`error\`

3. **CodeBlock** - 代码块
   \`\`\`tsx
   <CodeBlock language="javascript" title="可选标题">
   {\`
   const example = "代码内容";
   console.log(example);
   \`}
   </CodeBlock>
   \`\`\`

4. **Section** - 章节容器
   \`\`\`tsx
   <Section>
     <h2>章节标题</h2>
     <p>章节内容...</p>
   </Section>
   \`\`\`

5. **Divider** - 分隔线
   \`\`\`tsx
   <Divider />
   \`\`\`

### 最佳实践

- 使用 Section 组件包装每个主要章节
- 在章节之间使用 Divider 分隔
- 图片统一使用 ImageContainer 组件
- 代码块使用 CodeBlock 组件并指定正确的语言
- 重要提示使用 HighlightBox 组件
`;
}
